/**
 * Styly pro frontend modul "Cena na dotaz"
 * Všechny třídy maj<PERSON> prefix "pi-" (price inquiry) pro zamezení kolizí
 */

/* Kontejner pro tlačítko zjistit cenu */
.pi-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.pi-wrapper {
    text-align: center;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> konte<PERSON> pro tlačítko pod cenou */
.pi-container-simple {
    margin: 15px 0;
    text-align: center;
}

/* Tlačítko "Zjistit cenu" */
.pi-btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 180px;
    justify-content: center;
}

.pi-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.pi-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.pi-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Ikona v tlačítku */
.pi-btn .material-icons {
    font-size: 18px;
}

/* Informační text */
.pi-info {
    margin-top: 12px;
    margin-bottom: 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Informační text pro jednodušší kontejner */
.pi-info-simple {
    margin-top: 8px;
    margin-bottom: 0;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

/* Text "Cena na dotaz" místo ceny */
.pi-price-text {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    display: inline-block;
    padding: 8px 16px;
    background-color: #e7f3ff;
    border: 2px solid #007bff;
    border-radius: 6px;
    margin: 10px 0;
}

/* Skrytí původních prvků pro přidání do košíku - POUZE u produktů s naším modulem */
/* Tato pravidla se aplikují pouze na produkty, které mají kontejner s tlačítkem "zjistit cenu" */
.pi-container ~ .product-add-to-cart,
.pi-container-simple ~ .product-add-to-cart,
.pi-container ~ .add-to-cart,
.pi-container-simple ~ .add-to-cart,
.pi-container ~ .product-actions .add-to-cart,
.pi-container-simple ~ .product-actions .add-to-cart {
    display: none !important;
}

/* Alternativní způsob - skrytí tlačítek v rámci produktu, který obsahuje náš modul */
.product-miniature:has(.pi-container) .product-add-to-cart,
.product-miniature:has(.pi-container-simple) .product-add-to-cart,
.product-miniature:has(.pi-container) .add-to-cart,
.product-miniature:has(.pi-container-simple) .add-to-cart,
.product-container:has(.pi-container) .product-add-to-cart,
.product-container:has(.pi-container-simple) .product-add-to-cart,
.product-container:has(.pi-container) .add-to-cart,
.product-container:has(.pi-container-simple) .add-to-cart {
    display: none !important;
}

/* Oprava selectboxu pro potisk produktu - pouze pro stránky s naším modulem */
.pi-container ~ * .form-control-select,
.pi-container-simple ~ * .form-control-select {
    height: auto !important;
    min-height: 38px !important;
    line-height: 1.4 !important;
    padding: 8px 12px !important;
}

/* Oprava pro duplicitní "Cena na dotaz" texty */
.pi-price-text + .pi-price-text {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .pi-container {
        margin: 15px 0;
        padding: 12px;
    }

    .pi-btn {
        padding: 10px 20px;
        font-size: 14px;
        min-width: 160px;
    }

    .pi-price-text {
        font-size: 20px;
        padding: 6px 12px;
    }

    .pi-info {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .pi-btn {
        width: 100%;
        max-width: 280px;
    }

    .pi-price-text {
        font-size: 18px;
    }
}

/* ===== POPUP MODAL STYLY ===== */

/* Modal overlay */
.pi-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.pi-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

/* Modal content */
.pi-modal-content {
    position: relative;
    background: white;
    margin: 2% auto;
    padding: 0;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: piModalSlideIn 0.3s ease-out;
}

@keyframes piModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal header */
.pi-modal-header {
    background: #ffffff;
    color: #333333;
    padding: 20px 25px;
    position: relative;
    border-bottom: 1px solid #dee2e6;
}

.pi-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333333;
}

.pi-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: #333333;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.pi-modal-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* Modal body */
.pi-modal-body {
    padding: 25px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

/* Product info section */
.pi-product-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 25px;
}

.pi-product-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.pi-product-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.pi-product-reference {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

/* Form styling */
.pi-form-group {
    margin-bottom: 20px;
}

.pi-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.pi-form-group label.required .pi-required-star {
    color: #dc3545;
    margin-left: 2px;
}

.pi-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.pi-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.pi-form-control {
    resize: vertical;
    min-height: 80px;
}

/* Checkbox styling */
.pi-checkbox-group {
    margin-bottom: 25px;
}

.pi-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
}

.pi-checkbox-label input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    margin-top: 1px;
}

/* Messages */
.pi-messages {
    margin-bottom: 20px;
}

.pi-alert {
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
}

.pi-alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.pi-alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* Modal footer */
.pi-modal-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    margin-top: 25px;
}

.pi-btn-modal {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.pi-btn-primary {
    background-color: #007bff;
    color: white;
}

.pi-btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.pi-btn-primary:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.pi-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.pi-btn-secondary:hover {
    background-color: #545b62;
}

.pi-btn-loading {
    display: none;
}

/* Body class when modal is open */
body.pi-modal-open {
    overflow: hidden;
}

/* Responsive modal */
@media (max-width: 768px) {
    .pi-modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
    }

    .pi-modal-header {
        padding: 15px 20px;
    }

    .pi-modal-header h3 {
        font-size: 18px;
    }

    .pi-modal-body {
        padding: 20px;
    }

    .pi-product-info {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .pi-product-image img {
        width: 60px;
        height: 60px;
    }

    .pi-modal-footer {
        flex-direction: column-reverse;
        gap: 10px;
    }

    .pi-btn-modal {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .pi-modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 8px;
    }

    .pi-modal-header {
        padding: 12px 15px;
    }

    .pi-modal-body {
        padding: 15px;
    }

    .pi-form-control {
        padding: 8px 10px;
    }
}

/* Loading animation */
@keyframes piSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pi-fa-spin {
    animation: piSpin 1s linear infinite;
}
