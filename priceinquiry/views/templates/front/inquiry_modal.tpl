{*
* Popup formulář pro dotaz na cenu
* Template pro modal dialog
*}

<div id="piModal" class="pi-modal" style="display: none;">
    <div class="pi-modal-overlay"></div>
    <div class="pi-modal-content">
        <div class="pi-modal-header">
            <h3>{l s='Zjistit cenu produktu' mod='priceinquiry'}</h3>
            <button type="button" class="pi-modal-close" aria-label="{l s='Zavřít' mod='priceinquiry'}">
                <i class="material-icons">close</i>
            </button>
        </div>

        <div class="pi-modal-body">
            <form id="piForm" method="post" action="{$link->getModuleLink('priceinquiry', 'inquiry')|escape:'html':'UTF-8'}">

                {* Informace o produktu *}
                <div class="pi-product-info">
                    <div class="pi-product-image">
                        <img id="piProductImage" src="" alt="" />
                    </div>
                    <div class="pi-product-details">
                        <h4 id="piProductName"></h4>
                        <p class="pi-product-reference">
                            <strong>{l s='Kód produktu:' mod='priceinquiry'}</strong>
                            <span id="piProductReference"></span>
                        </p>
                    </div>
                </div>

                {* Skrytá pole s informacemi o produktu *}
                <input type="hidden" name="id_product" id="piProductId" value="" />
                <input type="hidden" name="product_name" id="piProductNameHidden" value="" />
                <input type="hidden" name="product_reference" id="piProductReferenceHidden" value="" />
                <input type="hidden" name="ajax" value="1" />

                {* Kontaktní údaje *}
                <div class="pi-form-group">
                    <label for="piCustomerName" class="required">
                        {l s='Jméno a příjmení' mod='priceinquiry'} <span class="pi-required-star">*</span>
                    </label>
                    <input type="text"
                           name="customer_name"
                           id="piCustomerName"
                           class="pi-form-control"
                           value="{if $customer.logged}{$customer.firstname|escape:'html':'UTF-8'} {$customer.lastname|escape:'html':'UTF-8'}{/if}"
                           required />
                </div>

                <div class="pi-form-group">
                    <label for="piCustomerEmail" class="required">
                        {l s='E-mailová adresa' mod='priceinquiry'} <span class="pi-required-star">*</span>
                    </label>
                    <input type="email"
                           name="customer_email"
                           id="piCustomerEmail"
                           class="pi-form-control"
                           value="{if $customer.logged}{$customer.email|escape:'html':'UTF-8'}{/if}"
                           required />
                </div>

                <div class="pi-form-group">
                    <label for="piCustomerPhone">
                        {l s='Telefon' mod='priceinquiry'}
                    </label>
                    <input type="tel"
                           name="customer_phone"
                           id="piCustomerPhone"
                           class="pi-form-control"
                           value="" />
                </div>

                <div class="pi-form-group">
                    <label for="piCustomerCompany">
                        {l s='Firma' mod='priceinquiry'}
                    </label>
                    <input type="text"
                           name="customer_company"
                           id="piCustomerCompany"
                           class="pi-form-control"
                           value="" />
                </div>

                {* Množství *}
                <div class="pi-form-group">
                    <label for="piQuantity">
                        {l s='Požadované množství' mod='priceinquiry'}
                    </label>
                    <input type="number"
                           name="quantity"
                           id="piQuantity"
                           class="pi-form-control"
                           value="1"
                           min="1" />
                </div>

                {* Zpráva *}
                <div class="pi-form-group">
                    <label for="piMessage">
                        {l s='Zpráva / Poznámka' mod='priceinquiry'}
                    </label>
                    <textarea name="message"
                              id="piMessage"
                              class="pi-form-control"
                              rows="4"
                              placeholder="{l s='Zde můžete uvést další požadavky nebo dotazy...' mod='priceinquiry'}"></textarea>
                </div>

                {* GDPR souhlas *}
                <div class="pi-form-group pi-checkbox-group">
                    <label class="pi-checkbox-label">
                        <input type="checkbox"
                               name="gdpr_consent"
                               id="piGdprConsent"
                               value="1"
                               required />
                        <span class="pi-checkmark"></span>
                        {l s='Souhlasím se zpracováním osobních údajů za účelem zpracování dotazu na cenu' mod='priceinquiry'} <span class="pi-required-star">*</span>
                    </label>
                </div>

                {* Chybové a úspěšné zprávy *}
                <div id="piMessages" class="pi-messages" style="display: none;">
                    <div id="piError" class="pi-alert pi-alert-danger" style="display: none;"></div>
                    <div id="piSuccess" class="pi-alert pi-alert-success" style="display: none;"></div>
                </div>

                {* Tlačítka *}
                <div class="pi-modal-footer">
                    <button type="submit" class="pi-btn-modal pi-btn-primary" id="piSubmitInquiry">
                        <span class="pi-btn-text">{l s='Odeslat dotaz' mod='priceinquiry'}</span>
                        <span class="pi-btn-loading" style="display: none;">
                            <i class="fa fa-spinner pi-fa-spin"></i> {l s='Odesílám...' mod='priceinquiry'}
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
