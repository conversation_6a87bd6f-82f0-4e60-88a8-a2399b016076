/**
 * JavaScript pro frontend modul "Cena na dotaz"
 * Všechny ID a třídy mají prefix "pi-" pro zamezení kolizí
 */

document.addEventListener('DOMContentLoaded', function() {

    // Inicializace modulu
    PriceInquiry.init();

});

/**
 * Hlavní objekt pro správu dotazů na cenu
 */
var PriceInquiry = {

    /**
     * Inicializace modulu
     */
    init: function() {
        this.bindEvents();
        this.hideAddToCartButtons();
    },

    /**
     * Navázání event listenerů
     */
    bindEvents: function() {
        // Kliknutí na tlačítko "Zjistit cenu"
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('pi-btn') ||
                e.target.closest('.pi-btn')) {

                e.preventDefault();
                e.stopPropagation();

                var button = e.target.classList.contains('pi-btn') ?
                           e.target : e.target.closest('.pi-btn');

                PriceInquiry.handleInquiryClick(button);
            }

            // Zavření modalu
            if (e.target.classList.contains('pi-modal-close') ||
                e.target.closest('.pi-modal-close') ||
                e.target.classList.contains('pi-modal-overlay')) {

                e.preventDefault();
                PriceInquiry.closeModal();
            }
        });

        // Zavření modalu klávesou ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('piModal').style.display !== 'none') {
                PriceInquiry.closeModal();
            }
        });

        // Odeslání formuláře
        document.addEventListener('submit', function(e) {
            if (e.target.id === 'piForm') {
                e.preventDefault();
                PriceInquiry.submitForm();
            }
        });
    },
    
    /**
     * Zpracování kliknutí na tlačítko "Zjistit cenu"
     */
    handleInquiryClick: function(button) {
        var productId = button.getAttribute('data-product-id');
        var productName = button.getAttribute('data-product-name');
        var productReference = button.getAttribute('data-product-reference') || '';
        var productImage = button.getAttribute('data-product-image') || '';
        var isLogged = button.getAttribute('data-is-logged') === '1';
        var customerName = button.getAttribute('data-customer-name') || '';
        var customerEmail = button.getAttribute('data-customer-email') || '';

        // Zobrazení loading stavu
        this.setButtonLoading(button, true);

        // Otevření popup formuláře
        setTimeout(function() {
            PriceInquiry.setButtonLoading(button, false);
            PriceInquiry.openModal(productId, productName, productReference, productImage, isLogged, customerName, customerEmail);
        }, 300);
    },
    
    /**
     * Nastavení loading stavu tlačítka
     */
    setButtonLoading: function(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            
            var originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="material-icons">hourglass_empty</i> Načítání...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            
            var originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    },
    
    /**
     * Skrytí tlačítek "Přidat do košíku" pouze pro produkty s cenou 0 Kč
     * NEPLATÍ pro vyprodané produkty - ty si ponechají své tlačítko (i když zašedlé)
     */
    hideAddToCartButtons: function() {
        // Najdeme všechny produkty s cenou na dotaz (pouze ty mají pi-container)
        var piContainers = document.querySelectorAll('.pi-container, .pi-container-simple');

        piContainers.forEach(function(container) {
            // Najdeme nejbližší product container
            var productContainer = container.closest('.product-miniature, .product-container, .product, .js-product');

            if (productContainer) {
                // Ověříme, zda má produkt skutečně cenu 0
                var hasZeroPrice = PriceInquiry.checkIfProductHasZeroPrice(productContainer);

                // Ověříme, zda NENÍ produkt vyprodaný (out of stock)
                var isOutOfStock = PriceInquiry.checkIfProductIsOutOfStock(productContainer);

                // Skryjeme tlačítka pouze pokud má cenu 0 A NENÍ vyprodaný
                if (hasZeroPrice && !isOutOfStock) {
                    // Skryjeme všechna tlačítka pro přidání do košíku v tomto produktu
                    var addToCartButtons = productContainer.querySelectorAll(
                        '.add-to-cart, .product-add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
                    );

                    addToCartButtons.forEach(function(btn) {
                        btn.style.display = 'none';
                    });
                }
            }
        });

        // Také skryjeme tlačítka na stránce produktu, ale pouze pokud je tam tlačítko "zjistit cenu"
        if (document.querySelector('.pi-btn')) {
            // Na stránce produktu kontrolujeme přítomnost pi-price-text nebo cenu 0
            var hasZeroPriceOnPage = document.querySelector('.pi-price-text') ||
                                   PriceInquiry.checkIfCurrentPageProductHasZeroPrice();

            // Kontrolujeme, zda NENÍ stránka vyprodaného produktu
            var isOutOfStockPage = PriceInquiry.checkIfCurrentPageProductIsOutOfStock();

            // Skryjeme tlačítka pouze pokud má cenu 0 A NENÍ vyprodaný
            if (hasZeroPriceOnPage && !isOutOfStockPage) {
                var productPageButtons = document.querySelectorAll(
                    '.product-add-to-cart, .add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
                );

                productPageButtons.forEach(function(btn) {
                    btn.style.display = 'none';
                });
            }
        }
    },

    /**
     * Kontrola, zda má produkt cenu 0 Kč
     */
    checkIfProductHasZeroPrice: function(productContainer) {
        // Hledáme indikátory ceny 0:
        // 1. Přítomnost pi-price-text (náš text "Cena na dotaz")
        if (productContainer.querySelector('.pi-price-text')) {
            return true;
        }

        // 2. Kontrola skutečné ceny v různých možných selektorech
        var priceSelectors = [
            '.current-price-value',
            '.price',
            '.product-price',
            '.current-price',
            '[data-price]'
        ];

        for (var i = 0; i < priceSelectors.length; i++) {
            var priceElement = productContainer.querySelector(priceSelectors[i]);
            if (priceElement) {
                var priceText = priceElement.textContent || priceElement.innerText || '';
                var priceValue = priceElement.getAttribute('data-price');

                // Kontrola textového obsahu na "0" nebo "0,00" nebo "0.00"
                if (priceText.match(/^0[,.]?0*\s*[Kk]?[čc]?$/)) {
                    return true;
                }

                // Kontrola data-price atributu
                if (priceValue && parseFloat(priceValue) === 0) {
                    return true;
                }
            }
        }

        return false;
    },

    /**
     * Kontrola ceny na stránce produktu
     */
    checkIfCurrentPageProductHasZeroPrice: function() {
        // Na stránce produktu hledáme cenu
        var priceSelectors = [
            '.current-price-value',
            '.product-price .current-price',
            '.price',
            '[data-price]'
        ];

        for (var i = 0; i < priceSelectors.length; i++) {
            var priceElement = document.querySelector(priceSelectors[i]);
            if (priceElement) {
                var priceText = priceElement.textContent || priceElement.innerText || '';
                var priceValue = priceElement.getAttribute('data-price');

                // Kontrola textového obsahu na "0" nebo "0,00" nebo "0.00"
                if (priceText.match(/^0[,.]?0*\s*[Kk]?[čc]?$/)) {
                    return true;
                }

                // Kontrola data-price atributu
                if (priceValue && parseFloat(priceValue) === 0) {
                    return true;
                }
            }
        }

        return false;
    },

    /**
     * Kontrola, zda je produkt vyprodaný (out of stock)
     */
    checkIfProductIsOutOfStock: function(productContainer) {
        // Hledáme indikátory vyprodaného produktu:

        // 1. CSS třídy pro vyprodané produkty
        var outOfStockSelectors = [
            '.product-unavailable',
            '.out-of-stock',
            '.unavailable',
            '.stock-out',
            '.no-stock'
        ];

        for (var i = 0; i < outOfStockSelectors.length; i++) {
            if (productContainer.querySelector(outOfStockSelectors[i])) {
                return true;
            }
        }

        // 2. Kontrola disabled tlačítek (typické pro vyprodané produkty)
        var addToCartButtons = productContainer.querySelectorAll(
            '.add-to-cart, .product-add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
        );

        var hasDisabledButton = false;
        addToCartButtons.forEach(function(btn) {
            if (btn.disabled || btn.hasAttribute('disabled') ||
                btn.classList.contains('disabled') || btn.classList.contains('btn-disabled')) {
                hasDisabledButton = true;
            }
        });

        // 3. Kontrola textu "vyprodáno", "není skladem" apod.
        var textContent = productContainer.textContent || productContainer.innerText || '';
        var outOfStockTexts = [
            'vyprodáno',
            'není skladem',
            'nedostupné',
            'out of stock',
            'unavailable',
            'sold out'
        ];

        var hasOutOfStockText = false;
        for (var j = 0; j < outOfStockTexts.length; j++) {
            if (textContent.toLowerCase().indexOf(outOfStockTexts[j]) !== -1) {
                hasOutOfStockText = true;
                break;
            }
        }

        // Produkt je vyprodaný pokud má disabled tlačítko NEBO text o vyprodání
        // (ale ne pokud má jen cenu 0 - to je náš případ)
        return hasDisabledButton || hasOutOfStockText;
    },

    /**
     * Kontrola, zda je aktuální stránka produktu vyprodaná
     */
    checkIfCurrentPageProductIsOutOfStock: function() {
        // Na stránce produktu hledáme indikátory vyprodání

        // 1. CSS třídy pro vyprodané produkty
        var outOfStockSelectors = [
            '.product-unavailable',
            '.out-of-stock',
            '.unavailable',
            '.stock-out',
            '.no-stock'
        ];

        for (var i = 0; i < outOfStockSelectors.length; i++) {
            if (document.querySelector(outOfStockSelectors[i])) {
                return true;
            }
        }

        // 2. Kontrola disabled tlačítek
        var addToCartButtons = document.querySelectorAll(
            '.product-add-to-cart, .add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
        );

        var hasDisabledButton = false;
        addToCartButtons.forEach(function(btn) {
            if (btn.disabled || btn.hasAttribute('disabled') ||
                btn.classList.contains('disabled') || btn.classList.contains('btn-disabled')) {
                hasDisabledButton = true;
            }
        });

        // 3. Kontrola textu "vyprodáno"
        var bodyText = document.body.textContent || document.body.innerText || '';
        var outOfStockTexts = [
            'vyprodáno',
            'není skladem',
            'nedostupné',
            'out of stock',
            'unavailable',
            'sold out'
        ];

        var hasOutOfStockText = false;
        for (var j = 0; j < outOfStockTexts.length; j++) {
            if (bodyText.toLowerCase().indexOf(outOfStockTexts[j]) !== -1) {
                hasOutOfStockText = true;
                break;
            }
        }

        return hasDisabledButton || hasOutOfStockText;
    },

    /**
     * Otevření popup modalu
     */
    openModal: function(productId, productName, productReference, productImage, isLogged, customerName, customerEmail) {
        var modal = document.getElementById('piModal');
        if (!modal) {
            console.error('Modal element not found');
            return;
        }

        // Vyplnění informací o produktu
        document.getElementById('piProductId').value = productId;
        document.getElementById('piProductNameHidden').value = productName;
        document.getElementById('piProductReferenceHidden').value = productReference;
        document.getElementById('piProductName').textContent = productName;
        document.getElementById('piProductReference').textContent = productReference;

        if (productImage) {
            document.getElementById('piProductImage').src = productImage;
            document.getElementById('piProductImage').alt = productName;
        }

        // Vyplnění údajů přihlášeného uživatele
        if (isLogged) {
            document.getElementById('piCustomerName').value = customerName;
            document.getElementById('piCustomerEmail').value = customerEmail;
        }

        // Vymazání předchozích zpráv
        this.hideMessages();

        // Zobrazení modalu
        modal.style.display = 'block';
        document.body.classList.add('pi-modal-open');

        // Focus na první pole
        setTimeout(function() {
            var firstInput = modal.querySelector('input:not([type="hidden"]):not([readonly])');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    },

    /**
     * Zavření popup modalu
     */
    closeModal: function() {
        var modal = document.getElementById('piModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.classList.remove('pi-modal-open');

            // Reset formuláře
            var form = document.getElementById('piForm');
            if (form) {
                form.reset();
            }

            this.hideMessages();
        }
    },

    /**
     * Odeslání formuláře pomocí AJAX
     */
    submitForm: function() {
        var form = document.getElementById('piForm');
        var submitBtn = document.getElementById('piSubmitInquiry');

        // Validace formuláře
        if (!this.validateForm()) {
            return;
        }

        // Nastavení loading stavu
        this.setSubmitLoading(true);
        this.hideMessages();

        // Příprava dat
        var formData = new FormData(form);

        // AJAX požadavek
        var xhr = new XMLHttpRequest();
        xhr.open('POST', form.action, true);

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                PriceInquiry.setSubmitLoading(false);

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        PriceInquiry.handleResponse(response);
                    } catch (e) {
                        PriceInquiry.showError('Došlo k chybě při zpracování odpovědi.');
                    }
                } else {
                    PriceInquiry.showError('Došlo k chybě při odesílání dotazu. Zkuste to prosím znovu.');
                }
            }
        };

        xhr.send(formData);
    },

    /**
     * Validace formuláře
     */
    validateForm: function() {
        var errors = [];

        // Jméno
        var name = document.getElementById('piCustomerName').value.trim();
        if (!name) {
            errors.push('Jméno a příjmení je povinné pole.');
        }

        // E-mail
        var email = document.getElementById('piCustomerEmail').value.trim();
        if (!email) {
            errors.push('E-mailová adresa je povinná.');
        } else if (!this.isValidEmail(email)) {
            errors.push('Zadejte platnou e-mailovou adresu.');
        }

        // GDPR souhlas
        var gdprConsent = document.getElementById('piGdprConsent').checked;
        if (!gdprConsent) {
            errors.push('Musíte souhlasit se zpracováním osobních údajů.');
        }

        if (errors.length > 0) {
            this.showError(errors.join('<br>'));
            return false;
        }

        return true;
    },

    /**
     * Validace e-mailové adresy
     */
    isValidEmail: function(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * Zpracování odpovědi ze serveru
     */
    handleResponse: function(response) {
        if (response.success) {
            this.showSuccess(response.message);

            // Zavření modalu po 2 sekundách
            setTimeout(function() {
                PriceInquiry.closeModal();
            }, 2000);
        } else {
            if (response.errors && response.errors.length > 0) {
                this.showError(response.errors.join('<br>'));
            } else {
                this.showError(response.message || 'Došlo k neočekávané chybě.');
            }
        }
    },

    /**
     * Zobrazení chybové zprávy
     */
    showError: function(message) {
        var messagesContainer = document.getElementById('piMessages');
        var errorDiv = document.getElementById('piError');

        errorDiv.innerHTML = message;
        errorDiv.style.display = 'block';
        document.getElementById('piSuccess').style.display = 'none';
        messagesContainer.style.display = 'block';

        // Scroll k chybě
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    },

    /**
     * Zobrazení úspěšné zprávy
     */
    showSuccess: function(message) {
        var messagesContainer = document.getElementById('piMessages');
        var successDiv = document.getElementById('piSuccess');

        successDiv.innerHTML = message;
        successDiv.style.display = 'block';
        document.getElementById('piError').style.display = 'none';
        messagesContainer.style.display = 'block';

        // Scroll k úspěchu
        successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    },

    /**
     * Skrytí všech zpráv
     */
    hideMessages: function() {
        var messagesContainer = document.getElementById('piMessages');
        if (messagesContainer) {
            messagesContainer.style.display = 'none';
            document.getElementById('piError').style.display = 'none';
            document.getElementById('piSuccess').style.display = 'none';
        }
    },

    /**
     * Nastavení loading stavu pro submit tlačítko
     */
    setSubmitLoading: function(loading) {
        var submitBtn = document.getElementById('piSubmitInquiry');
        var btnText = submitBtn.querySelector('.pi-btn-text');
        var btnLoading = submitBtn.querySelector('.pi-btn-loading');

        if (loading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }
    }

};

/**
 * CSS styly pro loading stav (přidáno dynamicky)
 */
if (!document.querySelector('#pi-dynamic-styles')) {
    var style = document.createElement('style');
    style.id = 'pi-dynamic-styles';
    style.textContent = `
        .pi-btn.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .pi-btn.loading:hover {
            transform: none;
            box-shadow: none;
        }
    `;
    document.head.appendChild(style);
}
